'use client';

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useStores } from '@/stores/utils/useStores';
// Header is now provided by the layout
import { Button } from '@/components/ui/button';
import { Box, Rocket, ExternalLink, AlertCircle, CheckCircle, Clock, Server, Search, MoreHorizontal, Filter, RefreshCw, Download, Globe, Smartphone } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Deployment, DeploymentPlatform } from '@/stores/DeploymentStore';
import { formatDistanceToNow, format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';
import DeploymentDialog from "@/components/deployment/DeploymentDialog";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {Project} from "@/lib/db/schema";

interface DeploymentsPageProps {
  chatId: string;
  project: Project;
}

const DeploymentsPage = observer(({ chatId, project }: DeploymentsPageProps) => {
  const { deploymentStore, notificationStore } = useStores();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deployments, setDeployments] = useState<Deployment[]>([]);

  // Extract loadDeployments function to be used both in useEffect and refresh button
  const loadDeployments = async () => {
    if (!project) return;
    
    setIsLoading(true);
    try {
      await deploymentStore.loadDeploymentsFromDatabase(project.id);
      // Safely get deployments after loading
      const loadedDeployments = deploymentStore.getDeployments(project.id);
      setDeployments(loadedDeployments);
    } catch (error) {
      console.error('Failed to load deployments:', error);
      notificationStore.showNotification({
        title: 'Error',
        message: 'Failed to load deployments',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDeployments();
  };

  // Load deployments when the component mounts
  useEffect(() => {
    loadDeployments();
    
    // Set up polling for in-progress deployments
    const pollingInterval = setInterval(() => {
      if (deployments.some(d => ['queued', 'processing'].includes(d.status))) {
        loadDeployments();
      }
    }, 5000);
    
    return () => clearInterval(pollingInterval);
  }, [project, deploymentStore, notificationStore]);

  // Sort deployments by creation date (newest first)
  const sortedDeployments = [...deployments].sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const renderStatusBadge = (status: string) => {
    const isInProgress = ['processing', 'queued', 'deploying'].includes(status);
    const isCompleted = status === 'completed' || status === 'success';
    const isFailed = status === 'failed';
    
    if (isInProgress) {
      return (
        <Badge variant="secondary" className="text-xs font-normal flex items-center gap-1 h-6">
          <Clock className="h-3 w-3 animate-pulse" />
          <span className="capitalize">In Progress</span>
        </Badge>
      );
    }
    
    if (isCompleted) {
      return (
        <Badge variant="outline" className="text-xs font-normal flex items-center gap-1 h-6 border-green-200 text-green-700 dark:text-green-400">
          <CheckCircle className="h-3 w-3" />
          <span className="capitalize">Completed</span>
        </Badge>
      );
    }
    
    if (isFailed) {
      return (
        <Badge variant="destructive" className="text-xs font-normal flex items-center gap-1 h-6">
          <AlertCircle className="h-3 w-3" />
          <span className="capitalize">Failed</span>
        </Badge>
      );
    }
    
    return (
      <Badge variant="outline" className="text-xs font-normal flex items-center gap-1 h-6">
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'success':
        return <CheckCircle className="h-3 w-3" />;
      case 'failed':
        return <AlertCircle className="h-3 w-3" />;
      case 'processing':
      case 'queued':
      case 'deploying':
        return <Clock className="h-3 w-3" />;
      default:
        return null;
    }
  };
  
  // Function to get the formatted URL for web deployments
  const getFormattedUrl = (deployment: Deployment): string => {
    if (deployment.platform === 'web' && deployment.slug) {
      return `https://${deployment.slug}.web.magically.life`;
    }
    return deployment.url || '';
  };
  
  // Function to get the platform icon
  const getPlatformIcon = (platform: DeploymentPlatform) => {
    switch (platform) {
      case 'web':
        return <Globe className="h-4 w-4 text-muted-foreground" />;
      case 'android':
        return <Smartphone className="h-4 w-4 text-muted-foreground" />;
      case 'ios':
        return <Smartphone className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Server className="h-4 w-4 text-muted-foreground" />;
    }
  };
  
  // Function to get action button text based on platform
  const getActionButtonText = (platform: DeploymentPlatform): string => {
    switch (platform) {
      case 'web':
        return 'View Website';
      case 'android':
        return 'Download APK';
      case 'ios':
        return 'Download IPA';
      default:
        return 'View';
    }
  };

  return (
    <div className="flex flex-col">

      <div className="flex-1 overflow-auto p-4 md:p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-semibold tracking-tight">Deployments</h1>
              <p className="text-sm text-muted-foreground mt-1">Manage and monitor your project deployments</p>
            </div>
            <Button onClick={() => setDialogOpen(true)} size="sm" className="h-9">
              <Rocket className="mr-2 h-4 w-4" />
              Deploy Project
            </Button>
          </div>
          
          <div className="flex items-center justify-between mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                type="search" 
                placeholder="Search deployments..." 
                className="pl-9 h-9 w-full md:w-[260px] text-sm"
              />
            </div>
            <div className="flex gap-2">
              <Button 
                variant="secondary" 
                onClick={handleRefresh} 
                disabled={isRefreshing} 
                className="h-9 gap-1.5"
              >
                <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" className="h-9 gap-1.5">
                <Filter className="h-3.5 w-3.5" />
                <span>Filter</span>
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="rounded-md border">
              <div className="p-4">
                <Skeleton className="h-8 w-full mb-4" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full mt-2" />
                <Skeleton className="h-20 w-full mt-2" />
              </div>
            </div>
          ) : deployments.length === 0 ? (
            <div className="text-center py-12 bg-card/50 rounded-lg border border-border/50">
              <Server className="mx-auto h-12 w-12 text-muted-foreground mb-4 opacity-80" />
              <h3 className="text-lg font-medium mb-2">No deployments yet</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">Deploy your project to make it accessible to users and stakeholders</p>
              <Button onClick={() => setDialogOpen(true)} className="h-9 gap-1.5 bg-primary hover:bg-primary/90">
                <Rocket className="h-4 w-4" />
                Deploy
              </Button>
            </div>
          ) : (
            <div className="rounded-md border overflow-hidden">
              <div className="relative w-full overflow-auto">
                <table className="w-full caption-bottom text-sm">
                  <thead className="[&_tr]:border-b">
                    <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                      <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Version</th>
                      <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Status</th>
                      <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Platform</th>
                      <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Created</th>
                      <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="[&_tr:last-child]:border-0">
                    {sortedDeployments.map((deployment) => {
                      const isInProgress = ['processing', 'queued'].includes(deployment.status);
                      return (
                        <tr 
                          key={deployment.id} 
                          className={cn(
                            "border-b transition-colors hover:bg-muted/50",
                            isInProgress && "bg-blue-50/30 dark:bg-blue-950/10"
                          )}
                        >
                          <td className="p-4 align-middle font-medium">{deployment.version}</td>
                          <td className="p-4 align-middle">{renderStatusBadge(deployment.status)}</td>
                          <td className="p-4 align-middle">
                            <div className="flex items-center gap-2">
                              {getPlatformIcon(deployment.platform as DeploymentPlatform)}
                              <span className="capitalize">{deployment.platform}</span>
                            </div>
                          </td>
                          <td className="p-4 align-middle text-muted-foreground">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span>{formatDistanceToNow(new Date(deployment.createdAt), { addSuffix: true })}</span>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{format(new Date(deployment.createdAt), 'PPpp')}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </td>
                          <td className="p-4 align-middle">
                            <div className="flex items-center gap-2">
                              {(deployment.url || deployment.slug) && 
                               (deployment.status === 'completed' || deployment.status === 'success') ? (
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="h-8 gap-1.5 text-muted-foreground hover:text-foreground"
                                  onClick={() => window.open(getFormattedUrl(deployment), '_blank')}
                                >
                                  {deployment.platform === 'web' ? (
                                    <ExternalLink className="h-3.5 w-3.5" />
                                  ) : (
                                    <Download className="h-3.5 w-3.5" />
                                  )}
                                  {getActionButtonText(deployment.platform as DeploymentPlatform)}
                                </Button>
                              ) : deployment.status === 'failed' ? (
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="h-8 gap-1.5 text-destructive hover:text-destructive/90"
                                >
                                  <AlertCircle className="h-3.5 w-3.5" />
                                  View Error
                                </Button>
                              ) : ['queued', 'processing', 'deploying'].includes(deployment.status) ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  disabled
                                  className="h-8 gap-1.5 text-muted-foreground cursor-not-allowed"
                                >
                                  <Clock className="h-3.5 w-3.5 animate-pulse" />
                                  In Progress
                                </Button>
                              ) : null}
                              
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  {(deployment.url || deployment.slug) && (
                                    <DropdownMenuItem onClick={() => navigator.clipboard.writeText(getFormattedUrl(deployment))}>
                                      Copy URL
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem>View details</DropdownMenuItem>
                                  <DropdownMenuItem>Redeploy</DropdownMenuItem>
                                  {deployment.status === 'failed' && (
                                    <DropdownMenuItem>View error logs</DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              {sortedDeployments.length > 10 && (
                <div className="flex items-center justify-end px-4 py-2 border-t">
                  <Button variant="outline" size="sm" className="h-8">
                    Load more
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <DeploymentDialog
        open={dialogOpen}
        onOpenChange={(open) => {
          setDialogOpen(open);
          // Auto-refresh the table when the dialog closes
          if (!open) {
            handleRefresh();
          }
        }}
        projectId={project.id}
      />
    </div>
  );
});

export default DeploymentsPage;
