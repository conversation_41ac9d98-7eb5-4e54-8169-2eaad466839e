"use client";

import React, { useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Upload, ImageIcon, X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadFile } from '@/services/Uploads';
import { toast } from 'sonner';
import Image from 'next/image';
import type { Attachment } from 'ai';

interface DesignInspirationUploadProps {
  attachments: Attachment[];
  onAttachmentsChange: (attachments: Attachment[]) => void;
}

export function DesignInspirationUpload({ attachments, onAttachmentsChange }: DesignInspirationUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadQueue, setUploadQueue] = useState<string[]>([]);

  // Process files (common function for both input change and drop)
  const processFiles = useCallback(
    async (files: File[]) => {
      if (!files.length) return;

      // Filter for JPG and PNG files only
      const allowedTypes = ['image/jpeg', 'image/png'];
      const imageFiles = files.filter(file => allowedTypes.includes(file.type));

      if (imageFiles.length === 0) {
        toast.error('Please upload JPG or PNG files only');
        return;
      }

      if (files.length !== imageFiles.length) {
        toast.info('Only JPG and PNG files will be uploaded');
      }

      // Add files to upload queue with their names
      setUploadQueue(imageFiles.map((file) => file.name));

      try {
        // Process each file upload individually to show progress
        for (const file of imageFiles) {
          const attachment = await uploadFile(file);
          if (attachment) {
            onAttachmentsChange([...attachments, attachment]);

            // Remove this file from the upload queue
            setUploadQueue(current => current.filter(name => name !== file.name));
          }
        }
      } catch (error) {
        console.error('Error uploading files!', error);
        toast.error('Failed to upload one or more files');
        setUploadQueue([]);
      }
    },
    [attachments, onAttachmentsChange],
  );

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  }, [isDragging]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    processFiles(files);
  }, [processFiles]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    processFiles(files);

    // Reset the file input to allow selecting the same file again
    if (e.target) {
      e.target.value = '';
    }
  }, [processFiles]);

  const removeAttachment = (index: number) => {
    const newAttachments = attachments.filter((_, i) => i !== index);
    onAttachmentsChange(newAttachments);
  };

  return (
    <div className="space-y-4">
      {/* Upload area */}
      <div
        className={cn(
          "relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 backdrop-blur-sm",
          isDragging
            ? "border-primary bg-gradient-to-br from-primary/10 to-accent/10 scale-[1.02] shadow-lg"
            : "border-white/40 dark:border-gray-600/40 hover:border-primary/50 hover:bg-white/30 dark:hover:bg-gray-700/30"
        )}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple
          accept="image/jpeg,image/png,.jpg,.jpeg,.png"
          onChange={handleFileInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />

        <div className="space-y-4">
          <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/10 to-accent/10 w-fit mx-auto backdrop-blur-sm border border-white/20">
            <Upload className="h-7 w-7 text-primary" />
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-1">Drop design inspiration here</h4>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Or click to browse • JPG, PNG up to 10MB each
            </p>
          </div>

          {isDragging && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl backdrop-blur-md"
            >
              <div className="text-primary font-semibold text-lg">Drop files here! ✨</div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Upload queue indicators */}
      {uploadQueue.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {uploadQueue.map((filename, idx) => (
            <div key={`${filename}-${idx}`} className="flex items-center gap-2 px-4 py-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-white/30">
              <Loader2 className="h-4 w-4 animate-spin text-primary" />
              <span className="text-sm text-gray-600 dark:text-gray-300">Uploading {filename}...</span>
            </div>
          ))}
        </div>
      )}

      {/* Uploaded attachments */}
      {attachments.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Design inspiration ({attachments.length})</h4>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <AnimatePresence>
              {attachments.map((attachment, index) => (
                <motion.div
                  key={attachment.url || index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="relative group"
                >
                  <div className="aspect-square rounded-xl border border-white/30 overflow-hidden bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm shadow-lg">
                    {attachment.contentType?.startsWith('image/') ? (
                      <Image
                        src={attachment.url || ''}
                        alt={attachment.name || 'Design inspiration'}
                        fill
                        className="object-cover"
                        unoptimized={true}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <button
                    className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full w-7 h-7 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg hover:scale-110"
                    onClick={() => removeAttachment(index)}
                    aria-label="Remove attachment"
                  >
                    <X className="w-4 h-4" />
                  </button>

                  {attachment.name && (
                    <div className="mt-2 text-xs text-gray-600 dark:text-gray-300 truncate text-center">
                      {attachment.name}
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  );
}
