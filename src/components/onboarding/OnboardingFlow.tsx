"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Sparkles,
  Users,
  Palette,
  Rocket,
  ArrowRight,
  ArrowLeft,
  X,
  Upload,
  Image as ImageIcon,
  Check
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DesignInspirationUpload } from "./DesignInspirationUpload";
import type { Attachment } from 'ai';

interface OnboardingData {
  appIdea: string;
  targetAudience: string;
  customAudience: string;
  designInspiration: Attachment[];
  projectType: 'design' | 'app';
}

interface OnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (data: OnboardingData) => void;
  onSkip: () => void;
}

const AUDIENCE_OPTIONS = [
  { value: 'general', label: 'General Users', description: 'Anyone can use this app' },
  { value: 'business', label: 'Business Professionals', description: 'For work and productivity' },
  { value: 'students', label: 'Students & Educators', description: 'For learning and education' },
  { value: 'health', label: 'Health & Wellness', description: 'For fitness and wellbeing' },
  { value: 'creative', label: 'Creative Professionals', description: 'For artists and designers' },
  { value: 'custom', label: 'Other', description: 'I\'ll specify my own audience' }
];

export function OnboardingFlow({ isOpen, onClose, onComplete, onSkip }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    appIdea: '',
    targetAudience: '',
    customAudience: '',
    designInspiration: [],
    projectType: 'app'
  });

  const totalSteps = 5;

  // Check if user has completed onboarding before
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem('hasCompletedOnboarding');
    if (hasCompletedOnboarding && !isOpen) {
      return;
    }
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onComplete(data);
  };

  const handleSkip = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onSkip();
  };

  const updateData = (field: keyof OnboardingData, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: return data.appIdea.trim().length > 10;
      case 1: return data.targetAudience !== '' && (data.targetAudience !== 'custom' || data.customAudience.trim() !== '');
      case 2: return true; // Design inspiration is optional
      case 3: return data.projectType !== '';
      case 4: return true; // Confirmation step
      default: return false;
    }
  };

  const stepVariants = {
    enter: { opacity: 0, x: 50 },
    center: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-background text-foreground p-0 overflow-hidden">
        {/* Header */}
        <div className="relative p-6 pb-4 border-b border-border">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-5 w-5" />
          </button>

          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-full bg-primary/10">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Welcome to Magically!</h2>
              <p className="text-sm text-muted-foreground">Let's create something amazing together</p>
            </div>
          </div>

          {/* Progress bar */}
          <div className="flex items-center gap-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "h-2 rounded-full transition-all duration-300",
                  i <= currentStep ? "bg-primary flex-1" : "bg-muted flex-1"
                )}
              />
            ))}
          </div>
          <div className="text-xs text-muted-foreground mt-2">
            Step {currentStep + 1} of {totalSteps}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              variants={stepVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {currentStep === 0 && (
                <WhatToBuildStep
                  value={data.appIdea}
                  onChange={(value) => updateData('appIdea', value)}
                />
              )}

              {currentStep === 1 && (
                <TargetAudienceStep
                  selectedAudience={data.targetAudience}
                  customAudience={data.customAudience}
                  onAudienceChange={(value) => updateData('targetAudience', value)}
                  onCustomAudienceChange={(value) => updateData('customAudience', value)}
                />
              )}

              {currentStep === 2 && (
                <DesignInspirationStep
                  attachments={data.designInspiration}
                  onAttachmentsChange={(attachments) => updateData('designInspiration', attachments)}
                />
              )}

              {currentStep === 3 && (
                <ProjectTypeStep
                  selectedType={data.projectType}
                  onTypeChange={(type) => updateData('projectType', type)}
                />
              )}

              {currentStep === 4 && (
                <ConfirmationStep data={data} />
              )}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 pt-0 border-t border-border">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={handleSkip}
              className="text-muted-foreground hover:text-foreground"
            >
              Skip onboarding
            </Button>

            <div className="flex items-center gap-3">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}

              <Button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="flex items-center gap-2 bg-primary hover:bg-primary/90"
              >
                {currentStep === totalSteps - 1 ? (
                  <>
                    <Rocket className="h-4 w-4" />
                    Create My App
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Individual step components
function WhatToBuildStep({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Sparkles className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">What would you like to build?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Describe your app idea in simple terms. Don't worry about technical details - just tell us what you envision!
        </p>
      </div>

      <div className="space-y-3">
        <Label htmlFor="app-idea">Your app idea</Label>
        <Textarea
          id="app-idea"
          placeholder="Example: A fitness app that helps people track their workouts and see their progress over time..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="min-h-[120px] resize-none"
        />
        <div className="text-xs text-muted-foreground">
          {value.length}/500 characters {value.length < 10 && "(minimum 10 characters)"}
        </div>
      </div>
    </div>
  );
}

function TargetAudienceStep({
  selectedAudience,
  customAudience,
  onAudienceChange,
  onCustomAudienceChange
}: {
  selectedAudience: string;
  customAudience: string;
  onAudienceChange: (value: string) => void;
  onCustomAudienceChange: (value: string) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Users className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">Who is this app for?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Understanding your target audience helps us create a better user experience and design.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {AUDIENCE_OPTIONS.map((option) => (
          <button
            key={option.value}
            onClick={() => onAudienceChange(option.value)}
            className={cn(
              "p-4 rounded-lg border text-left transition-all hover:border-primary/50",
              selectedAudience === option.value
                ? "border-primary bg-primary/5"
                : "border-border hover:bg-muted/50"
            )}
          >
            <div className="font-medium text-sm">{option.label}</div>
            <div className="text-xs text-muted-foreground mt-1">{option.description}</div>
          </button>
        ))}
      </div>

      {selectedAudience === 'custom' && (
        <div className="space-y-2">
          <Label htmlFor="custom-audience">Describe your target audience</Label>
          <Input
            id="custom-audience"
            placeholder="e.g., Small business owners, Parents with young children..."
            value={customAudience}
            onChange={(e) => onCustomAudienceChange(e.target.value)}
          />
        </div>
      )}
    </div>
  );
}

// Export the OnboardingData type for use in other components
export type { OnboardingData };
