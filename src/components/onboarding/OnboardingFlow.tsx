"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Sparkles,
  Users,
  Palette,
  Rocket,
  ArrowRight,
  ArrowLeft,
  X,
  Upload,
  Image as ImageIcon,
  Check,
  Wand2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DesignInspirationUpload } from "./DesignInspirationUpload";
import type { Attachment } from 'ai';

interface OnboardingData {
  appIdea: string;
  targetAudience: string;
  customAudience: string;
  designInspiration: Attachment[];
  projectType: 'design' | 'app';
}

interface OnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (data: OnboardingData) => void;
  onSkip: () => void;
  initialAppIdea?: string;
  initialAttachments?: Attachment[];
}

// Commented out for future use
// const AUDIENCE_OPTIONS = [
//   { value: 'general', label: 'General Users', description: 'Anyone can use this app' },
//   { value: 'business', label: 'Business Professionals', description: 'For work and productivity' },
//   { value: 'students', label: 'Students & Educators', description: 'For learning and education' },
//   { value: 'health', label: 'Health & Wellness', description: 'For fitness and wellbeing' },
//   { value: 'creative', label: 'Creative Professionals', description: 'For artists and designers' },
//   { value: 'custom', label: 'Other', description: 'I\'ll specify my own audience' }
// ];

export function OnboardingFlow({
  isOpen,
  onClose,
  onComplete,
  onSkip,
  initialAppIdea = '',
  initialAttachments = []
}: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    appIdea: initialAppIdea,
    targetAudience: 'general', // Default value
    customAudience: '',
    designInspiration: initialAttachments,
    projectType: 'design' // Default to design (most popular)
  });

  const totalSteps = 2; // Simplified to 2 steps

  // Check if user has completed onboarding before
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem('hasCompletedOnboarding');
    if (hasCompletedOnboarding && !isOpen) {
      return;
    }
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onComplete(data);
  };

  const handleSkip = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onSkip();
  };

  const updateData = (field: keyof OnboardingData, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: return data.appIdea.trim().length > 10;
      case 1: return data.targetAudience !== '' && (data.targetAudience !== 'custom' || data.customAudience.trim() !== '');
      case 2: return true; // Design inspiration is optional
      case 3: return data.projectType !== '';
      case 4: return true; // Confirmation step
      default: return false;
    }
  };

  const stepVariants = {
    enter: { opacity: 0, x: 50 },
    center: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-background text-foreground p-0 overflow-hidden">
        {/* Header */}
        <div className="relative p-6 pb-4 border-b border-border">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-5 w-5" />
          </button>

          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-full bg-primary/10">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Welcome to Magically!</h2>
              <p className="text-sm text-muted-foreground">Let's create something amazing together</p>
            </div>
          </div>

          {/* Progress bar */}
          <div className="flex items-center gap-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "h-2 rounded-full transition-all duration-300",
                  i <= currentStep ? "bg-primary flex-1" : "bg-muted flex-1"
                )}
              />
            ))}
          </div>
          <div className="text-xs text-muted-foreground mt-2">
            Step {currentStep + 1} of {totalSteps}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              variants={stepVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {currentStep === 0 && (
                <WhatToBuildStep
                  value={data.appIdea}
                  onChange={(value) => updateData('appIdea', value)}
                />
              )}

              {currentStep === 1 && (
                <TargetAudienceStep
                  selectedAudience={data.targetAudience}
                  customAudience={data.customAudience}
                  onAudienceChange={(value) => updateData('targetAudience', value)}
                  onCustomAudienceChange={(value) => updateData('customAudience', value)}
                />
              )}

              {currentStep === 2 && (
                <DesignInspirationStep
                  attachments={data.designInspiration}
                  onAttachmentsChange={(attachments) => updateData('designInspiration', attachments)}
                />
              )}

              {currentStep === 3 && (
                <ProjectTypeStep
                  selectedType={data.projectType}
                  onTypeChange={(type) => updateData('projectType', type)}
                />
              )}

              {currentStep === 4 && (
                <ConfirmationStep data={data} />
              )}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 pt-0 border-t border-border">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={handleSkip}
              className="text-muted-foreground hover:text-foreground"
            >
              Skip onboarding
            </Button>

            <div className="flex items-center gap-3">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}

              <Button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="flex items-center gap-2 bg-primary hover:bg-primary/90"
              >
                {currentStep === totalSteps - 1 ? (
                  <>
                    <Rocket className="h-4 w-4" />
                    Create My App
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Individual step components
function WhatToBuildStep({ value, onChange }: { value: string; onChange: (value: string) => void }) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Sparkles className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">What would you like to build?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Describe your app idea in simple terms. Don't worry about technical details - just tell us what you envision!
        </p>
      </div>

      <div className="space-y-3">
        <Label htmlFor="app-idea">Your app idea</Label>
        <Textarea
          id="app-idea"
          placeholder="Example: A fitness app that helps people track their workouts and see their progress over time..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="min-h-[120px] resize-none"
        />
        <div className="text-xs text-muted-foreground">
          {value.length}/500 characters {value.length < 10 && "(minimum 10 characters)"}
        </div>
      </div>
    </div>
  );
}

function TargetAudienceStep({
  selectedAudience,
  customAudience,
  onAudienceChange,
  onCustomAudienceChange
}: {
  selectedAudience: string;
  customAudience: string;
  onAudienceChange: (value: string) => void;
  onCustomAudienceChange: (value: string) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Users className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">Who is this app for?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Understanding your target audience helps us create a better user experience and design.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {AUDIENCE_OPTIONS.map((option) => (
          <button
            key={option.value}
            onClick={() => onAudienceChange(option.value)}
            className={cn(
              "p-4 rounded-lg border text-left transition-all hover:border-primary/50",
              selectedAudience === option.value
                ? "border-primary bg-primary/5"
                : "border-border hover:bg-muted/50"
            )}
          >
            <div className="font-medium text-sm">{option.label}</div>
            <div className="text-xs text-muted-foreground mt-1">{option.description}</div>
          </button>
        ))}
      </div>

      {selectedAudience === 'custom' && (
        <div className="space-y-2">
          <Label htmlFor="custom-audience">Describe your target audience</Label>
          <Input
            id="custom-audience"
            placeholder="e.g., Small business owners, Parents with young children..."
            value={customAudience}
            onChange={(e) => onCustomAudienceChange(e.target.value)}
          />
        </div>
      )}
    </div>
  );
}


function DesignInspirationStep({
  attachments,
  onAttachmentsChange
}: {
  attachments: Attachment[];
  onAttachmentsChange: (attachments: Attachment[]) => void;
}) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Palette className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">Do you have design inspiration?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Upload any images, mockups, or designs that inspire your app's look and feel.
          <span className="font-medium text-primary"> This step is completely optional!</span>
        </p>
      </div>

      <DesignInspirationUpload
        attachments={attachments}
        onAttachmentsChange={onAttachmentsChange}
      />

      {attachments.length === 0 && (
        <div className="text-center p-4 bg-muted/30 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 No worries! We'll create beautiful designs for you automatically.
          </p>
        </div>
      )}
    </div>
  );
}

function ProjectTypeStep({
  selectedType,
  onTypeChange
}: {
  selectedType: 'design' | 'app';
  onTypeChange: (type: 'design' | 'app') => void;
}) {
  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Rocket className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">How would you like to start?</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Choose your preferred approach to building your app.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <button
          onClick={() => onTypeChange('design')}
          className={cn(
            "p-6 rounded-lg border text-left transition-all hover:border-primary/50",
            selectedType === 'design'
              ? "border-primary bg-primary/5"
              : "border-border hover:bg-muted/50"
          )}
        >
          <div className="flex items-start gap-4">
            <div className="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/20">
              <Palette className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-sm mb-1">Design First ✨</div>
              <div className="text-xs text-muted-foreground mb-2">
                We'll create stunning visual designs and mockups for you to preview and approve before building the full app.
              </div>
              <div className="text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded">
                <strong>Next step:</strong> Design process → Review designs → Build app
              </div>
              <div className="text-xs text-primary mt-2 font-medium">
                Perfect for: Visual thinkers, specific design requirements
              </div>
            </div>
          </div>
        </button>

        <button
          onClick={() => onTypeChange('app')}
          className={cn(
            "p-6 rounded-lg border text-left transition-all hover:border-primary/50",
            selectedType === 'app'
              ? "border-primary bg-primary/5"
              : "border-border hover:bg-muted/50"
          )}
        >
          <div className="flex items-start gap-4">
            <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20">
              <Rocket className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-sm mb-1">Build App Directly 🚀</div>
              <div className="text-xs text-muted-foreground mb-2">
                Jump straight into building a functional app with smart design choices made automatically.
              </div>
              <div className="text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded">
                <strong>Next step:</strong> Start building your app immediately
              </div>
              <div className="text-xs text-primary mt-2 font-medium">
                Perfect for: Quick prototypes, functionality-focused apps
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>
  );
}

function ConfirmationStep({ data }: { data: OnboardingData }) {
  const getAudienceDisplay = () => {
    if (data.targetAudience === 'custom') {
      return data.customAudience;
    }
    const option = AUDIENCE_OPTIONS.find(opt => opt.value === data.targetAudience);
    return option?.label || data.targetAudience;
  };

  return (
    <div className="space-y-4">
      <div className="text-center space-y-2">
        <div className="p-3 rounded-full bg-primary/10 w-fit mx-auto">
          <Check className="h-6 w-6 text-primary" />
        </div>
        <h3 className="text-lg font-semibold">Ready to create your app!</h3>
        <p className="text-muted-foreground text-sm max-w-md mx-auto">
          Here's what we'll build for you based on your preferences.
        </p>
      </div>

      <div className="space-y-4 bg-muted/30 rounded-lg p-4">
        <div className="max-h-[150px] overflow-y-scroll">
          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">App Idea</div>
          <div className="text-sm">{data.appIdea}</div>
        </div>

        <div>
          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">Target Audience</div>
          <div className="text-sm">{getAudienceDisplay()}</div>
        </div>

        {data.designInspiration.length > 0 && (
          <div>
            <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">Design Inspiration</div>
            <div className="text-sm">{data.designInspiration.length} image(s) uploaded</div>
          </div>
        )}

        <div>
          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">Approach</div>
          <div className="text-sm">
            {data.projectType === 'design' ? 'Design First ✨' : 'Build App Directly 🚀'}
          </div>
        </div>
      </div>

      {data.projectType === 'design' && (
        <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Palette className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
            <div>
              <div className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
                Design Process Selected
              </div>
              <div className="text-xs text-purple-700 dark:text-purple-300">
                We'll create beautiful visual designs for your app first. You'll be able to review,
                provide feedback, and approve the designs before we build the full functional app.
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="text-center">
        <p className="text-xs text-muted-foreground">
          Click "Create My App" to start the magical process! ✨
        </p>
      </div>
    </div>
  );
}

// Export the OnboardingData type for use in other components
export type { OnboardingData };
