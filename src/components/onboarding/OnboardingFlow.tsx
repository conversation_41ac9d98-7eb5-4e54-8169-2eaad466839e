"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Sparkles, 
  Users, 
  Palette, 
  Rocket, 
  ArrowRight, 
  ArrowLeft, 
  X,
  Upload,
  Image as ImageIcon,
  Check
} from "lucide-react";
import { cn } from "@/lib/utils";
import { DesignInspirationUpload } from "./DesignInspirationUpload";
import type { Attachment } from 'ai';

interface OnboardingData {
  appIdea: string;
  targetAudience: string;
  customAudience: string;
  designInspiration: Attachment[];
  projectType: 'design' | 'app';
}

interface OnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (data: OnboardingData) => void;
  onSkip: () => void;
}

const AUDIENCE_OPTIONS = [
  { value: 'general', label: 'General Users', description: 'Anyone can use this app' },
  { value: 'business', label: 'Business Professionals', description: 'For work and productivity' },
  { value: 'students', label: 'Students & Educators', description: 'For learning and education' },
  { value: 'health', label: 'Health & Wellness', description: 'For fitness and wellbeing' },
  { value: 'creative', label: 'Creative Professionals', description: 'For artists and designers' },
  { value: 'custom', label: 'Other', description: 'I\'ll specify my own audience' }
];

export function OnboardingFlow({ isOpen, onClose, onComplete, onSkip }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState<OnboardingData>({
    appIdea: '',
    targetAudience: '',
    customAudience: '',
    designInspiration: [],
    projectType: 'app'
  });

  const totalSteps = 5;

  // Check if user has completed onboarding before
  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem('hasCompletedOnboarding');
    if (hasCompletedOnboarding && !isOpen) {
      return;
    }
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onComplete(data);
  };

  const handleSkip = () => {
    localStorage.setItem('hasCompletedOnboarding', 'true');
    onSkip();
  };

  const updateData = (field: keyof OnboardingData, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 0: return data.appIdea.trim().length > 10;
      case 1: return data.targetAudience !== '' && (data.targetAudience !== 'custom' || data.customAudience.trim() !== '');
      case 2: return true; // Design inspiration is optional
      case 3: return data.projectType !== '';
      case 4: return true; // Confirmation step
      default: return false;
    }
  };

  const stepVariants = {
    enter: { opacity: 0, x: 50 },
    center: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-background text-foreground p-0 overflow-hidden">
        {/* Header */}
        <div className="relative p-6 pb-4 border-b border-border">
          <button
            onClick={onClose}
            className="absolute right-4 top-4 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
          
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-full bg-primary/10">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">Welcome to Magically!</h2>
              <p className="text-sm text-muted-foreground">Let's create something amazing together</p>
            </div>
          </div>

          {/* Progress bar */}
          <div className="flex items-center gap-2">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div
                key={i}
                className={cn(
                  "h-2 rounded-full transition-all duration-300",
                  i <= currentStep ? "bg-primary flex-1" : "bg-muted flex-1"
                )}
              />
            ))}
          </div>
          <div className="text-xs text-muted-foreground mt-2">
            Step {currentStep + 1} of {totalSteps}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 min-h-[400px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              variants={stepVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {/* Step components will be added in the next part */}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="p-6 pt-0 border-t border-border">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={handleSkip}
              className="text-muted-foreground hover:text-foreground"
            >
              Skip onboarding
            </Button>
            
            <div className="flex items-center gap-3">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}
              
              <Button
                onClick={handleNext}
                disabled={!isStepValid()}
                className="flex items-center gap-2 bg-primary hover:bg-primary/90"
              >
                {currentStep === totalSteps - 1 ? (
                  <>
                    <Rocket className="h-4 w-4" />
                    Create My App
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the OnboardingData type for use in other components
export type { OnboardingData };
