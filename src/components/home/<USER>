import {useRouter} from "next/navigation";
import {useLocalStorage} from "usehooks-ts";
import {useState, useEffect, useRef, useTransition, useCallback, type ChangeEvent} from "react";
import {cn, generateUUID} from "@/lib/utils";
import {Button} from "@/components/ui/button";
import {Wand2, ImageIcon, XIcon} from "lucide-react";
import {useAuth} from "@/hooks/use-auth";
import {motion, AnimatePresence} from "framer-motion";
import {toast} from "sonner";
import {generateProject} from "@/app/(app)/projects/creating/actions";
import {useAnonymousSession} from "@/providers/anonymous-provider";
import {observer} from "mobx-react-lite";
import {useStores} from "@/stores/utils/useStores";
import { CreatingProjectDialog } from "./CreatingProjectDialog";
import type {Attachment} from 'ai';
import {PreviewAttachment} from "@/components/base/preview-attachment";
import Image from "next/image";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { trackIdeaFlowEvent } from "@/lib/analytics/track";
import {uploadFile} from "@/services/Uploads";
import {useFeatureFlagPayload} from "posthog-js/react";
import {JsonRecord} from "posthog-js";
import { OnboardingFlow, type OnboardingData } from "../onboarding/OnboardingFlow";

// Custom hook for animated placeholder text
function useAnimatedPlaceholder() {
    const [placeholderText, setPlaceholderText] = useState('');

    // Premium, high-intent prompt examples focused on innovative app ideas
    const prompts = [
        "Create a wellness app with personalized meditation and mood tracking",
        "Build a smart home dashboard with energy usage analytics",
        "Design a professional networking app with AI-powered connection suggestions",
        "Make a personal finance app with investment portfolio visualization"
    ];

    useEffect(() => {
        let currentPromptIndex = 0;
        let currentCharIndex = 0;
        let isTyping = true;
        let timeout: NodeJS.Timeout;

        const typeNextChar = () => {
            const currentPrompt = prompts[currentPromptIndex];

            if (isTyping) {
                // Typing forward
                if (currentCharIndex <= currentPrompt.length) {
                    setPlaceholderText(currentPrompt.substring(0, currentCharIndex));
                    currentCharIndex++;
                    timeout = setTimeout(typeNextChar, 50);
                } else {
                    // Pause at the end before erasing
                    isTyping = false;
                    timeout = setTimeout(typeNextChar, 2000);
                }
            } else {
                // Erasing
                if (currentCharIndex >= 0) {
                    setPlaceholderText(currentPrompt.substring(0, currentCharIndex));
                    currentCharIndex--;
                    timeout = setTimeout(typeNextChar, 30);
                } else {
                    // Move to next prompt
                    isTyping = true;
                    currentPromptIndex = (currentPromptIndex + 1) % prompts.length;
                    timeout = setTimeout(typeNextChar, 500);
                }
            }
        };

        // Start the animation
        timeout = setTimeout(typeNextChar, 500);

        // Cleanup
        return () => clearTimeout(timeout);
    }, []);

    return placeholderText;
}

const IdeaForm = observer(({prompt, setPrompt}: {prompt: string, setPrompt: (prompt: string) => any}) => {
    const router = useRouter();
    const {isAuthenticated, session} = useAuth();
    const [isTyping, setIsTyping] = useState(false);
    const [isPromptChanged, setIsPromptChanged] = useState(false);
    const [isCreatingProject, setIsCreatingProject] = useState(false);
    const [waitingForServerAction, setWaitingForServerAction] = useState(false);
    const prevPromptRef = useRef(prompt);
    const {generatorStore} = useStores();
    const [isPending, startTransition] = useTransition();
    const fileInputRef = useRef<HTMLInputElement>(null);
    const formRef = useRef<HTMLFormElement>(null);
    const [attachments, setAttachments] = useState<Array<Attachment>>([]);
    const [uploadQueue, setUploadQueue] = useState<Array<string>>([]);
    const [previewAttachment, setPreviewAttachment] = useState<Attachment | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [initialPrompt, setInitialPrompt] = useLocalStorage('initial_prompt', '');
    const payload = useFeatureFlagPayload('design-preview-feature')

    const {anonymousId, hasReachedLimit, incrementChatCount, storeChat, getStoredChat} = useAnonymousSession();

    // Track form view on component mount
    useEffect(() => {
        trackIdeaFlowEvent('FORM_VIEWED', {
            source: 'home_page'
        });
    }, []);

    useEffect(() => {
        if (initialPrompt) {
            try {
                const data = JSON.parse(initialPrompt);
                if (data.prompt) {
                    setPrompt(data.prompt);
                    setAttachments(data.attachments);
                }
                trackIdeaFlowEvent('AUTH_GUARD_CLEARED', {
                    prompt_length: prompt.trim().length,
                    has_attachments: attachments.length > 0,
                    attachment_count: attachments.length
                });
            } catch (e) {
                console.log('Error initializing the prompt')
            }
        }
    }, [initialPrompt])

    // Detect when prompt changes from external source (not typing)
    useEffect(() => {
        if (prompt !== prevPromptRef.current && !isTyping) {
            // Prompt was changed externally (from app suggestion)
            setIsPromptChanged(true);

            // Reset the animation after a delay
            const timer = setTimeout(() => {
                setIsPromptChanged(false);
            }, 1500);

            return () => clearTimeout(timer);
        }
        prevPromptRef.current = prompt;
    }, [prompt, isTyping]);

    // Function to adjust textarea height based on content
    const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
        if (!textarea) return;

        // Reset height to calculate the proper scrollHeight
        textarea.style.height = 'auto';

        // Calculate new height (capped at 200px)
        const newHeight = Math.min(Math.max(textarea.scrollHeight, 120), 200);
        textarea.style.height = `${newHeight}px`;
    };

    // Effect to adjust height when prompt changes externally
    useEffect(() => {
        const textarea = document.querySelector('textarea');
        if (textarea) adjustTextareaHeight(textarea as HTMLTextAreaElement);
    }, [prompt]);

    useEffect(() => {
        const lsPrompt = localStorage.getItem('project_prompt');
        if (lsPrompt) {
            setPrompt(lsPrompt);
        }
    }, []);

    // Process files (common function for both input change and drop)
    const processFiles = useCallback(
        async (files: File[]) => {
            if (!files.length) return;

            // Filter for JPG and PNG files only
            const allowedTypes = ['image/jpeg', 'image/png'];
            const imageFiles = files.filter(file => allowedTypes.includes(file.type));

            if (imageFiles.length === 0) {
                toast.error('Please upload JPG or PNG files only');
                return;
            }

            if (files.length !== imageFiles.length) {
                toast.info('Only JPG and PNG files will be uploaded');
            }

            // Add files to upload queue with their names
            setUploadQueue(imageFiles.map((file) => file.name));

            try {
                // Process each file upload individually to show progress
                for (const file of imageFiles) {
                    // Track image attachment attempt
                    trackIdeaFlowEvent('IMAGE_ATTACHED', {
                        file_type: file.type,
                        file_size: file.size
                    });

                    const attachment = await uploadFile(file);
                    if (attachment) {
                        setAttachments((currentAttachments) => [
                            ...currentAttachments,
                            attachment,
                        ]);

                        // Remove this file from the upload queue
                        setUploadQueue(current => current.filter(name => name !== file.name));
                    }
                }
            } catch (error) {
                console.error('Error uploading files!', error);
                toast.error('Failed to upload one or more files');
                setUploadQueue([]);
            }
        },
        [setAttachments],
    );

    // Handle file input change
    const handleFileChange = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files || []);
            processFiles(files);

            // Reset the file input to allow selecting the same file again
            if (event.target) {
                event.target.value = '';
            }
        },
        [processFiles],
    );

    // Drag and drop handlers
    const handleDragEnter = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isDragging) setIsDragging(true);
    }, [isDragging]);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const files = Array.from(e.dataTransfer.files);
        processFiles(files);
    }, [processFiles]);

    // In src/components/home/<USER>
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (prompt.trim()) {
            const destinationUrl = `/projects/creating?hasPrompt=1`;

            if(!isAuthenticated) {
                // Save the details to localStorage
                setInitialPrompt(JSON.stringify({
                    prompt: prompt.trim(),
                    attachments
                }))
                generatorStore.toggleLoginDialog(true);
                trackIdeaFlowEvent('AUTH_GUARD_TRIGGERRED', {
                    prompt_length: prompt.trim().length,
                    has_attachments: attachments.length > 0,
                    attachment_count: attachments.length
                });
                return;
            }
            // Track idea submission
            trackIdeaFlowEvent('IDEA_SUBMITTED', {
                prompt_length: prompt.trim().length,
                has_attachments: attachments.length > 0,
                attachment_count: attachments.length
            });

            // Show the creating project dialog
            setIsCreatingProject(true);

            const startTime = Date.now();

            const designPreviewEnabled = true || false;
            generateProject(
                prompt.trim(),
                isAuthenticated ? session?.user?.id as string : anonymousId as string,
                !isAuthenticated,
                attachments,
                designPreviewEnabled ? 'design': 'app' // Create a design chat instead of an app chat
            )
                .then(({project, chat}) => {
                    // Track successful project creation
                    trackIdeaFlowEvent('PROJECT_CREATED', {
                        project_id: project.id,
                        time_to_create: Date.now() - startTime
                    });

                    // Increment chat count for anonymous users
                    if (!isAuthenticated) {
                        incrementChatCount();
                    }
                    setPrompt('')
                    setInitialPrompt('');
                    setAttachments([]);
                    // Close the dialog and redirect
                    setTimeout(() => {
                        startTransition(() => {
                            router.push(`/projects/${project.id}/${designPreviewEnabled ? 'design': 'chats'}/${chat.id}`)
                            setWaitingForServerAction(true);
                        });
                    }, 1000);
                })
                .catch((error) => {
                    // Track project creation failure
                    trackIdeaFlowEvent('PROJECT_CREATION_FAILED', {
                        error_type: error.message?.includes("429") ? 'rate_limit' : 'server_error',
                        error_message: error.message || 'Unknown error'
                    });

                    console.error('Error creating project:', error);

                    // Close the dialog on error
                    setIsCreatingProject(false);
                    setWaitingForServerAction(false);

                    if (error.message?.includes("429")) {
                        toast.message("Your daily limit of messages is reached. Please login to continue using magically.");
                        generatorStore.toggleLoginDialog(true);
                    } else {
                        toast.error("Error processing your request");
                    }
                });
        }
    };

    useEffect(() => {
        if(waitingForServerAction && !isPending) {
            setWaitingForServerAction(false);
            setIsCreatingProject(false);
        }
    }, [isPending, isCreatingProject]);

    return (
        <>
            <form
                ref={formRef}
                onSubmit={handleSubmit}
                className="relative group"
                onDragEnter={handleDragEnter}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
            >
                {/* Hidden file input - only accept JPG and PNG */}
                <input
                    type="file"
                    className="fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none"
                    ref={fileInputRef}
                    multiple
                    accept="image/jpeg,image/png,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                    tabIndex={-1}
                />

                {/* Preview dialog for attachments */}
                <Dialog open={!!previewAttachment} onOpenChange={() => setPreviewAttachment(null)}>
                    <DialogContent className="sm:max-w-md">
                        {previewAttachment?.contentType?.startsWith('image/') ? (
                            <div className="relative w-full h-[300px] md:h-[500px]">
                                <Image
                                    src={previewAttachment.url || ''}
                                    alt={previewAttachment.name || 'Attachment'}
                                    fill
                                    className="object-contain"
                                    unoptimized={true}
                                />
                            </div>
                        ) : (
                            <div className="p-4 text-center">
                                <p className="text-lg font-medium">{previewAttachment?.name}</p>
                                <a
                                    href={previewAttachment?.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-primary hover:underline mt-2 inline-block"
                                >
                                    Open file
                                </a>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
                {/* Background gradient for typing state or drag state */}
                <div className={cn(
                    'absolute inset-0 rounded-xl',
                    'transition-all duration-300',
                    isDragging ? 'bg-primary/20 border-2 border-dashed border-primary/50 backdrop-blur-md' :
                    isTyping ? 'bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10 opacity-100 backdrop-blur-lg shadow-inner border border-white/10' : 'opacity-0 backdrop-blur-sm'
                )} />

                {/* Drag overlay with instructions */}
                {isDragging && (
                    <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
                        <div className="bg-background/60 backdrop-blur-lg p-6 rounded-xl border border-white/20 shadow-lg flex flex-col items-center space-y-2">
                            <ImageIcon className="w-10 h-10 text-primary/80" />
                            <p className="text-sm font-medium">Drop images here</p>
                            <p className="text-xs text-muted-foreground">Only JPG and PNG files are supported</p>
                        </div>
                    </div>
                )}

                {/* Animation for prompt change */}
                <AnimatePresence>
                    {isPromptChanged && (
                        <motion.div
                            className="absolute inset-0 rounded-xl bg-primary/20 z-[8]"
                            initial={{ opacity: 0, scale: 0.98 }}
                            animate={{
                                opacity: [0, 0.7, 0.3, 0],
                                scale: [0.98, 1.01, 1, 0.99]
                            }}
                            transition={{
                                duration: 1.2,
                                times: [0, 0.2, 0.6, 1],
                                ease: "easeInOut"
                            }}
                        />
                    )}
                </AnimatePresence>
                <div className="relative">
                    {/* Attachments inside the input box at the top */}
                    {(attachments.length > 0 || uploadQueue.length > 0) && (
                        <div className="absolute top-3 left-3 right-3 z-10">
                            <div className="flex items-center gap-2 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
                                {/* Upload indicators - show first */}
                                {uploadQueue.map((filename, idx) => (
                                    <div key={`${filename}-${idx}`} className="relative flex-shrink-0">
                                        <div className="w-[42px] h-[42px] rounded-md border border-border overflow-hidden bg-muted flex items-center justify-center">
                                            <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                                        </div>
                                    </div>
                                ))}

                                {/* Existing attachments */}
                                {attachments.map((attachment, index) => (
                                    <div key={attachment.url || index} className="relative group flex-shrink-0">
                                        <div
                                            className="w-[42px] h-[42px] rounded-md border border-border overflow-hidden cursor-pointer hover:border-primary hover:shadow-sm transition-all"
                                            onClick={() => setPreviewAttachment(attachment)}
                                        >
                                            {attachment.contentType?.startsWith('image/') ? (
                                                <Image
                                                    src={attachment.url || ''}
                                                    alt={attachment.name || 'Attachment'}
                                                    width={42}
                                                    height={42}
                                                    className="object-cover"
                                                    unoptimized={true}
                                                />
                                            ) : (
                                                <div className="w-full h-full flex items-center justify-center bg-muted">
                                                    <span className="text-xs">{(attachment.name || 'file').split('.').pop()}</span>
                                                </div>
                                            )}
                                        </div>
                                        <button
                                            className="absolute -top-1.5 -right-1.5 bg-destructive text-white rounded-full w-5 h-5 flex items-center justify-center opacity-80 md:opacity-0 md:group-hover:opacity-100 transition-opacity shadow-sm touch-manipulation"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setAttachments(prev => prev.filter((_, i) => i !== index));
                                            }}
                                            aria-label="Remove attachment"
                                        >
                                            <XIcon className="w-3 h-3" />
                                        </button>
                                    </div>
                                ))}
                            </div>
                            {(attachments.length + uploadQueue.length) > 3 && (
                                <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-secondary to-transparent pointer-events-none"></div>
                            )}
                        </div>
                    )}

                    <textarea
                        placeholder={useAnimatedPlaceholder()}
                        className={cn(
                            'w-full px-6 pb-14 rounded-xl',
                            'transition-all duration-300 bg-secondary',
                            'border-2 border-muted-foreground/20',
                            'placeholder:text-muted-foreground/60 placeholder:font-normal',
                            'shadow-sm group-hover:shadow-md focus:shadow-lg',
                            'relative z-[9] overflow-y-auto',
                            isTyping && 'shadow-lg scale-[1.01] border-primary/40 bg-background/80',
                            (attachments.length > 0 || uploadQueue.length > 0)
                                ? 'pt-16 min-h-[190px] max-h-[350px]'
                                : 'pt-4 min-h-[160px] max-h-[300px]'
                        )}
                        value={prompt}
                        onChange={(e) => {
                            setPrompt(e.target.value);
                            // Auto-resize textarea based on content
                            adjustTextareaHeight(e.target);
                        }}
                        onFocus={() => setIsTyping(true)}
                        onBlur={() => setIsTyping(false)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleSubmit(e);
                            }
                        }}
                        ref={(textareaRef) => {
                            // Initial height adjustment when component mounts or prompt changes
                            if (textareaRef) adjustTextareaHeight(textareaRef);
                        }}
                        style={{
                            lineHeight: '1.5',
                            resize: 'none',
                            overflowY: 'auto'
                        }}
                    />
                </div>


                <div
                    className={cn(
                        'absolute bottom-3 flex items-center gap-2 z-20',
                        'left-3 right-3 justify-between'
                    )}
                >
                    {/* Left side with attachment button */}
                    <div className="flex items-center gap-2">
                        {/* Attachment button */}
                        <Button
                            size="icon"
                            variant="ghost"
                            type="button"
                            className="rounded-full p-2 h-fit border dark:border-zinc-600"
                            onClick={(event) => {
                                event.preventDefault();
                                fileInputRef.current?.click();
                            }}
                            disabled={uploadQueue.length > 0}
                            title="Add image"
                        >
                            <ImageIcon className="h-5 w-5"/>
                        </Button>
                    </div>

                    {/* Right side with submit button */}
                    <div className="flex items-center gap-2">
                        <div className="text-xs text-muted-foreground/60 hidden group-hover:block transition-all duration-200">
                            Press Enter ↵
                        </div>
                        <Button
                            size="icon"
                            variant="default"
                            type="submit"
                            className={cn(
                                'bg-primary/90 hover:bg-primary',
                                'shadow-md hover:shadow-lg',
                                'transition-all duration-300'
                            )}
                            disabled={uploadQueue.length > 0}
                        >
                            <Wand2 className="h-5 w-5"/>
                        </Button>
                    </div>
                </div>
            </form>

            {/* Creating Project Dialog */}
            <CreatingProjectDialog
                isOpen={isCreatingProject}
                onOpenChange={setIsCreatingProject}
                isProcessing={isCreatingProject || isPending || waitingForServerAction}
            />
        </>
    )
})

export default IdeaForm;