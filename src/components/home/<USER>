import {Avatar, AvatarImage} from "@/components/ui/avatar";

export default function CreatorCount() {
    return (
        <div className="flex items-center px-4 py-2 rounded-full bg-background/20 backdrop-blur-md border border-white/10 shadow-lg shadow-black/5">
            <div className="flex -space-x-1 mr-2">
                {[1, 2, 3].map((i) => (
                    <Avatar key={i} className="w-6 h-6">
                        <AvatarImage src={`/faces/face-${i}.png`}  />
                    </Avatar>
                ))}
            </div>
            <span className="text-white/90">10,000+ creators</span>
        </div>
    )
}