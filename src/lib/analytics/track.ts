'use client';

import { ANALYTICS_EVENTS } from './events';
import posthog from 'posthog-js';

type BaseEventProperties = {
  source?: string;
  timestamp?: string;
  session_id?: string;
  device_type?: string;
  browser_info?: string;
  referrer?: string;
  page_url?: string;
};

export type SubscriptionEventProperties = BaseEventProperties & {
  plan_type?: string;
  current_plan?: string;
  price?: number;
  currency?: string;
  message_limit_remaining?: number;
  error_message?: string;
  trigger_reason?: string;
  time_viewed?: number;
  entry_point?: string;
  payment_method?: string;
};

export type MessageEventProperties = BaseEventProperties & {
  message_type?: string;
  generation_time_ms?: number;
  token_count?: number;
  error_type?: string;
  error_message?: string;
  is_pro_user?: boolean;
  chat_id?: string;
  message_id?: string;
  message_content_length?: number;
  has_attachments?: boolean;
  attachment_count?: number;
  total_messages_sent?: number;
  total_messages_received?: number;
  session_message_count?: number;
  original_length?: number;
  new_length?: number;
  time_elapsed?: number;
  completion_percentage?: number;
  time_in_conversation?: number;
  time_since_last_activity?: number;
  project_id?: string;
  response_time?: number;
};

export type IdeaFlowEventProperties = BaseEventProperties & {
  prompt_length?: number;
  has_attachments?: boolean;
  attachment_count?: number;
  file_type?: string;
  file_size?: number;
  project_id?: string;
  chat_id?: string;
  time_to_create?: number;
  error_type?: string;
  error_message?: string;
  time_to_first_preview?: number;
};

export type FeatureEventProperties = BaseEventProperties & {
  feature_name?: string;
  is_enabled?: boolean;
  user_type?: 'free' | 'pro';
  prompt_category?: string;
  file_count?: number;
  directory_depth?: number;
  file_type?: string;
  file_path?: string;
  edit_size?: number;
  trigger_source?: string;
  new_state?: boolean;
  previous_state?: boolean;
  project_id?: string;
  time_to_connect?: number;
  platform_type?: string;
  deployment_time?: number;
  snippet_length?: number;
  language?: string;
};

export type OnboardingEventProperties = BaseEventProperties & {
  entry_point?: string;
  step_number?: number;
  time_on_step?: number;
  total_time?: number;
  steps_completed?: number;
  step_skipped_at?: number;
  resource_type?: string;
  topic?: string;
  feature_name?: string;
  tooltip_id?: string;
  time_visible?: number;
};

export type ErrorEventProperties = BaseEventProperties & {
  error_type?: string;
  error_message?: string;
  component?: string;
  stack_trace?: string;
  user_action?: string;
  resource_type?: string;
  url?: string;
};

export type PerformanceEventProperties = BaseEventProperties & {
  page_url?: string;
  load_time_ms?: number;
  api_endpoint?: string;
  response_time_ms?: number;
  request_size?: number;
  operation_count?: number;
  credit_cost?: number;
};

export type UserEventProperties = BaseEventProperties & {
  anonymous_id?: string;
  referral_source?: string;
  auth_method?: string;
  time_to_complete?: number;
  error_type?: string;
};

// Helper to add common properties to all events
const addCommonProperties = (properties: Record<string, any> = {}) => {
  if (typeof window === 'undefined') return properties;
  
  return {
    ...properties,
    timestamp: new Date().toISOString(),
    environment: process.env.NEXT_PUBLIC_ENV,
    page_url: window.location.href,
    referrer: document.referrer,
    device_type: getDeviceType(),
    browser_info: navigator.userAgent,
    session_id: getOrCreateSessionId(),
  };
};

// Helper to determine device type
const getDeviceType = (): string => {
  if (typeof navigator === 'undefined') return 'unknown';
  
  const ua = navigator.userAgent;
  if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
    return 'tablet';
  }
  if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
    return 'mobile';
  }
  return 'desktop';
};

// Helper to get or create session ID
const getOrCreateSessionId = (): string => {
  if (typeof window === 'undefined' || typeof sessionStorage === 'undefined') {
    return 'server-side';
  }
  
  let sessionId = sessionStorage.getItem('magically_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    sessionStorage.setItem('magically_session_id', sessionId);
  }
  return sessionId;
};

// Safe PostHog capture that works on both client and server
const safeCapture = (eventName: string, properties: Record<string, any> = {}) => {
  try {
    // Only capture events on the client side
    if (typeof window !== 'undefined' && posthog) {
      posthog.capture(eventName, addCommonProperties(properties));
    }
  } catch (error) {
    console.error('Error capturing analytics event:', error);
  }
};

// User events
export const trackUserEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.USER,
  properties: UserEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.USER[eventName], properties);
};

// Subscription events
export const trackSubscriptionEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.SUBSCRIPTION,
  properties: SubscriptionEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.SUBSCRIPTION[eventName], properties);
};

// Message events
export const trackMessageEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.MESSAGES,
  properties: MessageEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.MESSAGES[eventName], properties);
};

// Idea flow events
export const trackIdeaFlowEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.IDEA_FLOW,
  properties: IdeaFlowEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.IDEA_FLOW[eventName], properties);
};

// Feature usage events
export const trackFeatureEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.FEATURES,
  properties: FeatureEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.FEATURES[eventName], properties);
};

// Onboarding events
export const trackOnboardingEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.ONBOARDING,
  properties: OnboardingEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.ONBOARDING[eventName], properties);
};

// Error events
export const trackErrorEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.ERROR,
  properties: ErrorEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.ERROR[eventName], properties);
};

// Performance events
export const trackPerformanceEvent = (
  eventName: keyof typeof ANALYTICS_EVENTS.PERFORMANCE,
  properties: PerformanceEventProperties = {}
) => {
  safeCapture(ANALYTICS_EVENTS.PERFORMANCE[eventName], properties);
};

// Generic event tracking
export const trackEvent = (eventName: string, properties: Record<string, any> = {}) => {
  safeCapture(eventName, properties);
};

// Page view tracking
export const trackPageView = (pageName: string, properties: BaseEventProperties = {}) => {
  safeCapture('page_view', {
    ...properties,
    page_name: pageName
  });
};
