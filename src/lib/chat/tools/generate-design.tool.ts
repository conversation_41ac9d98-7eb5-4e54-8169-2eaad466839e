import { z } from 'zod';
import { DataStreamWriter, tool, streamText } from 'ai';
import { generateUUID } from '@/lib/utils';
import { saveDesignScreen, updateDesignScreen, countDesignScreensByProject } from '@/lib/db/design-screens';
import { extractHtmlContent, resetHtmlExtractor } from '@/lib/utils/html-utils';
import { SCREEN_GENERATOR_SYSTEM_PROMPT } from '@/lib/ai/prompt-directory/design-generator-prompt';
import { customModel } from '@/lib/ai';
import { fetchFromOpenRouter } from '@/lib/openrouter/get-message-details';
import { exportData } from '@/lib/server-utils';

/**
 * Creates a visually appealing loading placeholder with the screen name
 */
function createLoadingPlaceholder(screenName: string): string {
  return `
    <div class="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100 p-6 text-center">
      <div class="mb-6 w-full max-w-md">
        <div class="text-2xl font-bold text-indigo-700 mb-2">${screenName}</div>
        <div class="text-sm text-indigo-500 mb-6">Crafting your design with care...</div>
        
        <div class="relative h-2 bg-indigo-100 rounded-full overflow-hidden">
          <div class="absolute top-0 left-0 h-full w-full bg-indigo-600 rounded-full animate-pulse"></div>
        </div>
      </div>
      
      <div class="flex space-x-2 justify-center">
        <div class="w-3 h-3 rounded-full bg-indigo-600 animate-bounce"></div>
        <div class="w-3 h-3 rounded-full bg-indigo-600 animate-bounce" style="animation-delay: 0.2s"></div>
        <div class="w-3 h-3 rounded-full bg-indigo-600 animate-bounce" style="animation-delay: 0.4s"></div>
      </div>
    </div>
  `;
}

// Schema for the design generation parameters
export const generateDesignSchema = z.object({
  appIdea: z.string().describe('Overall description of the app'),
  designSystem: z.object({
    colorScheme: z.enum(['light', 'dark', 'auto']).optional(),
    primaryColor: z.string().optional(),
    secondaryColor: z.string().optional()
  }).optional(),
  coherencePrompt: z.string().describe('Include details that should be coherent across the screens like navigation, header styles, logo design. Include sample code if possible. This is the most important prompt that sets up the process to ensure that all the screens are truly part of the same app design language.'),
  screens: z.array(z.object({
    order: z.number().describe('Unique ordering sequence for this page in the artboard. Example: Login/signup is 1 and checkout is 5. Basically, the user can see these screens in a sequence to understand the flow of the screens'),
    name: z.string().describe('Name of the screen. When editing a screen, please pass the existing screen name in the exact same case to match against the existing entries.'),
    description: z.string().describe('Description of what this screen should contain.'),
    mode: z.enum(['create', 'replace']).default('create'),
  })).describe("Please pass only the screens you want to create/edit. Any screens passed here will be regenerated entirely.")
});

/**
 * Tool for generating design screens for an app
 * 
 * This tool:
 * 1. Takes app idea, design system, and screen descriptions
 * 2. Generates HTML for each screen
 * 3. Saves the screens to the database
 * 4. Streams updates to the client
 */
// Maximum number of screens that can be generated per project
const MAX_TOTAL_SCREENS_LIMIT = 6;

export const generateDesign = ({
  dataStream,
  chatId,
  projectId,
  existingScreens = [],
  maxParallelScreens,
  maxTotalScreens = MAX_TOTAL_SCREENS_LIMIT // Default to 6 total screens, but allow configuration
}: {
  dataStream: DataStreamWriter;
  chatId: string;
  projectId: string;
  existingScreens: Array<{
    id: string;
    name: string;
    html: string;
    status: 'starting' | 'generating' | 'complete' | 'error';
  }>;
  maxParallelScreens: number;
  maxTotalScreens?: number; // Optional parameter to override the default total limit
}) => {
  return tool({
    description: 'Generate designs for the app based on user requirements',
    parameters: generateDesignSchema,
    execute: async ({ appIdea, designSystem, screens, coherencePrompt }) => {
      try {
        // console.log('Starting', screens)
        // Get the current count of screens for this project
        const existingScreenCount = existingScreens.length;
        
        // Calculate how many new screens we're trying to add (excluding replacements)
        const newScreensCount = screens.filter(screen => screen.mode === 'create').length;
        
        // Calculate the projected total after this operation
        const projectedTotalScreens = existingScreenCount + newScreensCount;
        
        // Check if the total number of screens would exceed the maximum limit
        if (projectedTotalScreens > Math.max(maxTotalScreens, existingScreenCount)) {
          console.warn(`Project would have ${projectedTotalScreens} screens, but only ${maxTotalScreens} are allowed`);
          
          // Return an informative error that the LLM can understand and handle
          return {
            error: `Screen limit exceeded. This project already has ${existingScreenCount} screens, and adding ${newScreensCount} more would exceed the maximum of ${maxTotalScreens} total screens allowed per project. Please remove some existing screens or reduce the number of new screens.`,
            currentScreenCount: existingScreenCount,
            requestedNewScreens: newScreensCount,
            projectedTotal: projectedTotalScreens,
            maxAllowedScreens: maxTotalScreens,
            result: 'error'
          };
        }
        // Define proper type for results
        interface ScreenResult {
          name: string;
          screenId: string;
          status: 'generating' | 'complete' | 'error';
        }

        console.log('Generating designs for screens:', screens.map(s => s.name).join(', '));

        const results: ScreenResult[] = await Promise.all(screens.map(async screen => {
          // Generate a stable ID for this screen
          let screenId = generateUUID();

          const existingScreen = existingScreens.find(s => s.name === screen.name);
          let existingContent: string | null = null;

          if (existingScreen) {
            screenId = existingScreen.id;
            existingContent = existingScreen.html;
            // Create initial database entry with generating status
            await updateDesignScreen({
              id: existingScreen.id,
              html: existingContent || createLoadingPlaceholder(screen.name),
              status: "generating",
              order: screen.order
            });
          } else {
            // Create initial database entry with generating status
            await saveDesignScreen({
              id: screenId,
              chatId,
              projectId,
              name: screen.name,
              html: createLoadingPlaceholder(screen.name),
              status: 'generating'
            });
          }

          // Send initial status to client
          dataStream.writeData({
            type: 'design-screen-update',
            content: {
              name: screen.name,
              screenId,
              status: 'generating',
              updatedAt: new Date().toISOString(),
              order: screen.order
            }
          });

          // Generate the screen HTML
          let screenHtml = '';

          // Use the AI SDK with the model
          const result = streamText({
            model: customModel("anthropic/claude-sonnet-4"),
            temperature: 0.7,
            messages: [
              {
                role: 'system',
                content: SCREEN_GENERATOR_SYSTEM_PROMPT +
                  '\n\nYou are generating a single screen for an app. ' +
                  'Focus only on creating the HTML for this specific screen. ' +
                  'Write the least code to achieve the results. COST reduction is key.' +
                  'Make sure to have the clickable to behave like React Native like with opacity transition on click.'
              },
              {
                role: 'user',
                content: [
                  {
                    type: 'text',
                    text: `Pay close attention to the coherence guidelines (Match it to its entirety. This ensures each screen is part of a cohesive design system): ${coherencePrompt}. The bottom navigation if present, must always be position fixed and attached to the bottom of the screen.`
                  },
                  {
                    type: 'text',
                    text: `\`App Idea: ${appIdea}\\n\\nDesign System:${JSON.stringify(designSystem, null, 2)}\\n\\Screen Name: ${screen.name}\\n\\nScreen Description: ${screen.description}\\n\\n${screen.mode === 'replace' ? `Existing Content (improve this and return the improved content in the screen tag): ${existingContent}` : 'NOT found. This is a new screen'}\``
                  }
                ]
              }
            ]
          });

          // Process the stream
          for await (const chunk of result.textStream) {
            screenHtml += chunk;

            // Only update the client periodically to avoid overwhelming the connection
            if (screenHtml.length % 100 === 0) {
              dataStream.writeData({
                type: 'design-screen-update',
                content: {
                  name: screen.name,
                  screenId,
                  status: 'generating',
                  updatedAt: new Date().toISOString(),
                  order: screen.order
                }
              });

              // Reset HTML extractor state before processing to prevent nesting
              resetHtmlExtractor();
              const html = extractHtmlContent(screenHtml);

              // For debugging purposes
              // if (process.env.NODE_ENV === 'development') {
              //   await exportData(html, 'response-messages');
              // }

              await updateDesignScreen({
                id: screenId,
                html: html || createLoadingPlaceholder(screen.name),
                status: "generating",
                order: screen.order
              });
            }
          }

          const response = await result.response;

          // Track usage for billing purposes
          try {
            const usage = await fetchFromOpenRouter(response?.id);
            console.log('Design generation usage:', usage);
          } catch (error) {
            console.error('Error fetching usage data:', error);
          }

          // Extract the HTML content from the response
          resetHtmlExtractor(); // Reset one final time before extracting the complete HTML
          const finalHtml = extractHtmlContent(screenHtml) || screenHtml;

          // Update the database with the final HTML
          await updateDesignScreen({
            id: screenId,
            html: finalHtml,
            status: 'complete',
            order: screen.order
          });

          // Send completion notification
          dataStream.writeData({
            type: 'design-screen-update',
            content: {
              name: screen.name,
              screenId,
              status: 'complete',
              updatedAt: new Date().toISOString(),
              order: screen.order
            }
          });

          return {
            name: screen.name,
            screenId,
            status: 'complete',
            html: finalHtml,
            order: screen.order
          };
        }));

        // Get the final count of screens for this project after this operation
        const finalScreenCount = await countDesignScreensByProject({ projectId });
        
        return {
          screens: results,
          screenCount: screens.length,
          currentProjectScreens: finalScreenCount,
          maxAllowedScreens: maxTotalScreens,
          remainingScreens: maxTotalScreens - finalScreenCount,
          result: 'success'
        };
      } catch (error) {
        console.error('Error generating design:', error);
        dataStream.writeData({
          type: 'error',
          error: error instanceof Error ? error.message : 'Failed to generate design'
        });
        throw error;
      }
    }
  });
};
